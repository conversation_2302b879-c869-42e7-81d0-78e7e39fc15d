module.exports = function (redis) {
    return async function (req, res, next) {
        const ip = "*************";
        // const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;

        const requestOptions = {
            method: "GET",
            redirect: "follow"
        };

        const cacheKey = `geo:${ip.replace('.', '')}`;

        redis.get(cacheKey, (err, result) => {
            if (!err) {
                if (result) {
                    console.log('cache');
                    req.geo = JSON.parse(result);
                    next();
                }                
            }
        });

        // fallback
        const geo = await fetch("https://api.ipgeolocation.io/v2/ipgeo?apiKey=********************************&ip=" + ip, requestOptions)
            .then((response) => response.json())
            .then((result) => { return result; })
            .catch((error) => console.log(error));

        req.geo = geo;
        redis.set(cacheKey, JSON.stringify(geo));

        console.log('no cache');

        next();
    }
}