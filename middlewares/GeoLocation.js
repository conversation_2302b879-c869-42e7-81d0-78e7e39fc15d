require('dotenv').config({ quiet: true });

module.exports = function (redis, db) {
    return async function (req, res, next) {
        const ip = process.env.APP_ENV === 'local' ? "*************" : (req.headers['x-forwarded-for'] || req.socket.remoteAddress);

        const requestOptions = {
            method: "GET",
            redirect: "follow"
        };

        const cacheKey = `geo:${ip.replace(/\./g, '')}`;

        const cachedValue = await redis.get(cacheKey).then((result) => {
            return result;
        }).catch((error) => { return null; });

        if (cachedValue) {
            req.geo = JSON.parse(cachedValue);
            next();
            return;
        }

        const geo = await fetch("https://api.ipgeolocation.io/v2/ipgeo?apiKey=" + process.env.IPLOCATION_API_KEY + "&ip=" + ip, requestOptions)
            .then((response) => response.json())
            .then((result) => { return result; })
            .catch((error) => { return null; });

        req.geo = Object.assign({}, geo, { location: { country: null, region: null }});
        redis.set(cacheKey, geo ? JSON.stringify(geo) : null);
        next();
    }
}