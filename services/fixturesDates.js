const knex = require("../db/knex");

async function getIds(params) {
    return await knex
        .select(['fixtures_dates_ids.fixture_id'])
        .from('fixtures_dates')
        .join('timezones_offsets', 'fixtures_dates.offset_id', 'timezones_offsets.id')
        .join('fixtures_dates_ids', 'fixtures_dates_ids.fixture_date_id', 'fixtures_dates.id')
        .where('fixtures_dates.date', params.date)
        .where('timezones_offsets.offset', params.tz_offset);
}

module.exports = {
    getIds
};