const knex = require("../db/knex");
const redis = require("../redis");
const fixturesDates = require("./fixturesDates");

async function getFixturesForADay(params) {
    console.log(params);
    const fixturesIdsCacheKey = 'fixtures:ids:' + params.date + ':' + params.tz_offset;
    let fixturesIds = await redis.get(fixturesIdsCacheKey);    

    console.log('from cache');
    console.log(fixturesIds);

    if (!fixturesIds) {
        console.log('no cache');
        fixturesIds = await fixturesDates.getIds(params);       
        
        console.log('query');
        console.log(fixturesIds);

        await redis.set(fixturesIdsCacheKey, fixturesIds);
    }
    else {
        console.log('cached');
    }

    console.log('post');
    console.log(fixturesIds);

    return await knex
        .select(['id', 'date'])
        .from('fixtures')
        .limit(1);
}

module.exports = {
    getFixturesForADay
};