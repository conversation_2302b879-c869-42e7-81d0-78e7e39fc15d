const mysql = require("mysql2/promise");

const credentials = {
    host: process.env['DB_HOST'],
    user: process.env['DB_USERNAME'],
    password: process.env['DB_PASSWORD'],
    database: process.env['DB_DATABASE'],
    port: process.env['DB_PORT']
};

async function connect() {
    return await mysql.createConnection({
        host: credentials.host,
        user: credentials.user,
        password: credentials.password,
        database: credentials.database,
        port: credentials.port
    });
}

async function getFixturesForADay(params) {
    try {
        const connection = await connect();
        const query = `
            SELECT 
                fixtures_dates_ids.fixture_id 
            FROM fixtures_dates 
            JOIN timezones_offsets ON fixtures_dates.offset_id = timezones_offsets.id
            JOIN fixtures_dates_ids ON fixtures_dates_ids.fixture_date_id = fixtures_dates.id
            WHERE 
                fixtures_dates.date = ? AND
                timezones_offsets.offset = ?
        `;
        const [rows, fields] = await connection.query(query, [params.date, params.tz_offset]);
        
        const fixtureIds = rows.map(item => item.fixture_id);

        const countryLeftJoin = params.country ? 'AND countries_leagues_order.country_id = ' + params.country.id : '';
        const regionLeftJoin = params.region ? 'AND regions_leagues_order.region_id = ' + params.region.id : '';

        const leaguesCondition = params.league_id ? 'AND fixtures.league_id IN (' + params.league_id : ')';
        const isLiveCondition = params.is_live ? 'AND fixtures.is_live = 1' : '';

        const fixtureQuery = `
            SELECT 
                fixtures.*,
                fixtures.the_sports_id as the_sports_id,
                fixtures.league_id
            FROM fixtures
            JOIN leagues ON leagues.id = fixtures.league_id AND leagues.in_use = 1
            LEFT JOIN countries_leagues_order ON countries_leagues_order.league_id = leagues.id ${countryLeftJoin}
            LEFT JOIN regions_leagues_order ON regions_leagues_order.league_id = leagues.id ${regionLeftJoin}
            WHERE 
                fixtures.id IN (?) ${leaguesCondition} ${isLiveCondition}
            ORDER BY 
                CASE WHEN countries_leagues_order._order IS NOT NULL THEN countries_leagues_order._order ELSE 99999 END ASC,
                CASE WHEN regions_leagues_order._order IS NOT NULL THEN regions_leagues_order._order ELSE 99999 END ASC,
                fixtures.has_predictions DESC,
                fixtures.date ASC
        `;
        const [fixtures, fields1] = await connection.query(fixtureQuery, [fixtureIds]);

        await connection.end();

        return fixtures;
    } catch (error) {
        console.log(error);
    }

    return false;
}

async function selectUsers() {
    try {
        const connection = await connect();
        const [rows, fields] = await connection.query('SELECT id, name FROM users');
        await connection.end();

        return rows;
    } catch (error) {
        console.log(error);
    }

    return false;
}

module.exports = {
    getFixturesForADay,
    selectUsers
};