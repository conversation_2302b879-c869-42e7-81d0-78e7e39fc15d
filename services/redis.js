const { createClient } = require('redis');

const client = createClient();

client.on('error', err => console.log('Redis Client Error', err));
client.on('connect', () => console.log('Redis Client Connected'));

async function connect() {
    return await client.connect();
}

async function save(key, value) {
    const connection = await connect();
    await connection.set(key, value);

    //return await client.quit();
}

async function get(key) {
    //const connection = await connect();
    return await client.get(key);

    //return await client.quit();
}

module.exports = {
    save,
    get
};