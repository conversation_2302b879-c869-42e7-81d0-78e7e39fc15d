const Redis = require("ioredis");
const redis = new Redis();
const _ = require("lodash");

async function get(cacheKey) {
    if (!cacheKey) {
        return null;
    }

    return await redis.get(cacheKey).then((result) => {
        console.log('get');
        console.log('result');
        return result;
    }).catch((error) => { return null; });
}

async function set(cacheKey, value) {
    if (!cacheKey) {
        return;
    }

    if (Array.isArray(value)) {
        await redis.lpush(cacheKey, value.length > 0 ? value : null);
        return;
    }

    await redis.set(cacheKey, value);
}

async function del(cacheKey) {
    if (!cacheKey) {
        return;
    }

    await redis.del(cacheKey);
}


module.exports = {
    get,
    set,
    del
};