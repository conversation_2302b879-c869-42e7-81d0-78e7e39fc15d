const Redis = require("ioredis");
const redis = new Redis();
const _ = require("lodash");

async function get(cacheKey) {
    console.log('lrange');
    console.log(await redis.lrange(cacheKey, 0, -1));

    if (!cacheKey) {
        return null;
    }

    return await redis.get(cacheKey).then((result) => {
        console.log('get');
        console.log('result');
        return result;
    }).catch((error) => { return null; });
}

async function set(cacheKey, value) {
    if (!cacheKey) {
        return;
    }

    if (Array.isArray(value)) {
        return await redis.set(cacheKey, JSON.stringify(value));
    }

    return await redis.set(cacheKey, value);
}

async function del(cacheKey) {
    if (!cacheKey) {
        return;
    }

    await redis.del(cacheKey);
}


module.exports = {
    get,
    set,
    del
};