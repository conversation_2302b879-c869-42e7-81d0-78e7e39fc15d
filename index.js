require('dotenv').config({ quiet: true });

const Redis = require("ioredis");
const express = require("express");
const app = express();
const port = 3000;

const db = require("./services/db");
//const redis = require("./services/redis");
const redis = new Redis();
const geoLocation = require("./middlewares/GeoLocation");

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(geoLocation(redis));

app.get('/', async (req, res) => {
    const users = await db.selectUsers();

    console.log('--');
    console.log(req.geo);
    console.log('--');

    res.json(users);
});

app.listen(port, () => {
    console.log(`Example app listening on port ${port}`);
});
