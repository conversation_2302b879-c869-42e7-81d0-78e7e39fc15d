require('dotenv').config({ quiet: true });

const Redis = require("ioredis");
const express = require("express");
const dayjs = require('dayjs');
const timezone = require('dayjs/plugin/timezone');
const utc = require('dayjs/plugin/utc');

dayjs.extend(utc);
dayjs.extend(timezone);

const date = dayjs.tz(new Date(), "America/Toronto").format("YYYY-MM-DD");
const tz_offset = '+01:00'/*dayjs.tz(new Date(), "America/Toronto").format("Z")*/;

const app = express();
const port = 3000;

const db = require("./services/db");
const redis = new Redis();
const geoLocation = require("./middlewares/GeoLocation");

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(geoLocation(redis, db));

app.get('/', async (req, res) => {
    const fixtures_dates = await db.getFixturesForADay({ 
        date: date, 
        tz_offset: tz_offset, 
        country: req.geo?.country, 
        region: req.geo?.region, 
        league_id: req.query.league_id, 
        is_live: req.query.is_live 
    });

    res.json(fixtures_dates);
});

app.listen(port, () => {
    console.log(`Example app listening on port ${port}`);
});
